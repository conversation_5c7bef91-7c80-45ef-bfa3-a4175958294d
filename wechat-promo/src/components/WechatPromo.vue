<template>
  <div class="wechat-promo">
    <!-- 头部标题 -->
    <header class="header">
      <div class="header-content">
        <h1 class="title">📚 学习软件激活指南</h1>
        <p class="subtitle">微信小程序 · 轻松开通学习权限</p>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 微信小程序码 -->
      <section class="qrcode-section">
        <h2 class="section-title">📱 微信小程序码</h2>
        <div class="qrcode-card">
          <div class="qrcode-container">
            <img
              :src="qrcodeUrl"
              alt="微信小程序码"
              class="qrcode-image"
              @error="handleImageError"
            />
            <p class="qrcode-tip">长按保存到相册，然后用微信扫一扫打开</p>
          </div>
          <div class="qrcode-actions">
            <button class="save-btn" @click="saveQRCode">
              💾 保存到相册
            </button>
            <button class="scan-btn" @click="showScanTip">
              📷 扫码说明
            </button>
          </div>
        </div>
      </section>

      <!-- 步骤说明 -->
      <section class="steps-section">
        <h2 class="section-title">🚀 开通步骤</h2>
        
        <div class="step-card">
          <div class="step-number">1</div>
          <div class="step-content">
            <h3>保存小程序码</h3>
            <p>将我们提供的<strong>微信小程序码</strong>保存到您的手机相册</p>
          </div>
        </div>

        <div class="step-card">
          <div class="step-number">2</div>
          <div class="step-content">
            <h3>扫码打开小程序</h3>
            <p>打开微信，使用<strong>扫一扫</strong>功能，从相册中找到保存的二维码打开小程序</p>
          </div>
        </div>

        <div class="step-card">
          <div class="step-number">3</div>
          <div class="step-content">
            <h3>点击立即开通</h3>
            <p>在首页点击<strong>【立即开通】</strong>按钮，选择您购买的对应类别年卡</p>
          </div>
        </div>

        <div class="step-card">
          <div class="step-number">4</div>
          <div class="step-content">
            <h3>输入兑换码</h3>
            <p>在兑换页面点击<strong>【立即兑换】</strong>，输入拼多多发给您的8位兑换码</p>
          </div>
        </div>
      </section>

      <!-- 重要提示 -->
      <section class="notice-section">
        <h2 class="section-title">⚠️ 重要提示</h2>
        <div class="notice-card">
          <div class="notice-item">
            <span class="notice-icon">💡</span>
            <p>如果购买的是<strong>【双册年卡】</strong>，需要分别选择每册，每次只输入一个兑换码</p>
          </div>
          <div class="notice-item">
            <span class="notice-icon">🔢</span>
            <p>每个兑换码的长度固定为<strong>8位字符</strong></p>
          </div>
        </div>
      </section>

      <!-- 好评奖励 -->
      <section class="reward-section">
        <h2 class="section-title">🎁 好评奖励</h2>
        <div class="reward-card">
          <div class="reward-header">
            <h3>给好评，送半年！</h3>
            <p class="reward-desc">成功兑换后，如果觉得产品不错，欢迎给我们好评</p>
          </div>
          
          <div class="reward-requirements">
            <h4>好评要求：</h4>
            <ul>
              <li>✅ 20字以上的使用评价</li>
              <li>✅ 使用配图</li>
              <li>✅ 5分好评</li>
            </ul>
          </div>

          <div class="reward-benefit">
            <div class="benefit-highlight">
              <span class="benefit-icon">🎉</span>
              <strong>奖励：免费续期半年使用时长</strong>
            </div>
          </div>

          <div class="reward-process">
            <h4>领取流程：</h4>
            <ol>
              <li>完成5分好评</li>
              <li>截屏发给客服</li>
              <li>24-48小时内审核</li>
              <li>在已兑换页面查看结果</li>
            </ol>
          </div>
        </div>
      </section>

      <!-- 快捷操作 -->
      <section class="quick-actions">
        <div class="action-card">
          <h3>🔧 快捷操作</h3>
          <div class="action-buttons">
            <button class="action-btn" @click="copyCode">
              📋 复制兑换码格式
            </button>
            <button class="action-btn" @click="showQRTips">
              📱 扫码技巧
            </button>
          </div>
        </div>
      </section>

      <!-- 联系客服 -->
      <section class="contact-section">
        <div class="contact-card">
          <h3>📞 需要帮助？</h3>
          <p>如有疑问，请联系客服获取支持</p>
          <button class="contact-btn" @click="showContact">联系客服</button>
        </div>
      </section>
    </main>

    <!-- 底部 -->
    <footer class="footer">
      <p>© 2024 学习软件 · 让学习更简单</p>
    </footer>

    <!-- 返回顶部按钮 -->
    <button
      v-show="showBackToTop"
      class="back-to-top"
      @click="scrollToTop"
    >
      ↑
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const showBackToTop = ref(false)

// 二维码URL，您可以替换为实际的微信小程序码图片
// 如果您有PNG格式的二维码，请将其保存为 /public/images/wx_qrcode.jpg
const qrcodeUrl = ref('/public/images/wx_qrcode.jpg')

const showContact = () => {
  alert('请通过拼多多订单页面联系客服，或查看商品详情页的客服联系方式')
}

const copyCode = () => {
  const text = '兑换码格式：8位字符（如：ABC12345）'
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      alert('兑换码格式已复制到剪贴板！')
    }).catch(() => {
      alert(text)
    })
  } else {
    alert(text)
  }
}

const showQRTips = () => {
  alert(`扫码小技巧：
1. 确保二维码图片清晰
2. 在光线充足的环境下扫码
3. 手机距离二维码适中（10-20cm）
4. 如果扫不出来，可以尝试调整角度`)
}

const handleImageError = (event) => {
  // 如果PNG加载失败，尝试加载SVG占位图
  if (event.target.src.includes('.png')) {
    event.target.src = '/images/wx_qrcode.svg'
    qrcodeUrl.value = '/images/wx_qrcode.svg'
  } else {
    // 如果SVG也加载失败，显示错误信息
    event.target.style.display = 'none'
    const errorMsg = document.createElement('div')
    errorMsg.className = 'qrcode-error'
    errorMsg.innerHTML = `
      <div style="padding: 2rem; text-align: center; color: #666; background: #f8f9fa; border-radius: 8px;">
        <p>📱 二维码暂时无法显示</p>
        <p style="font-size: 0.9rem; margin-top: 0.5rem;">请将您的微信小程序码图片保存为 wx_qrcode.png 并放置在 /public/images/ 目录下</p>
      </div>
    `
    event.target.parentNode.insertBefore(errorMsg, event.target)
  }
}

const saveQRCode = () => {
  alert(`保存二维码步骤：
1. 长按上方二维码图片
2. 选择"保存到相册"
3. 打开微信，点击右上角"+"
4. 选择"扫一扫"
5. 点击右上角相册图标
6. 选择刚保存的二维码`)
}

const showScanTip = () => {
  alert(`使用微信扫码：
1. 打开微信APP
2. 点击右上角"+"号
3. 选择"扫一扫"
4. 对准二维码扫描
5. 或点击右上角相册图标选择保存的二维码`)
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.wechat-promo {
  width: 100%;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 1rem;
  text-align: center;
}

.header-content {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

.title {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1rem;
  opacity: 0.9;
}

.main-content {
  padding: 1rem 0.8rem;
  background: #f5f5f5;
  width: 100%;
}

.section-title {
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #333;
  text-align: center;
}

.qrcode-section {
  margin-bottom: 2rem;
}

.qrcode-card {
  background: white;
  border-radius: 16px;
  padding: 2rem 1.5rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  text-align: center;
}

.qrcode-container {
  margin-bottom: 1.5rem;
}

.qrcode-image {
  width: 100%;
  max-width: 300px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  margin-bottom: 1rem;
  transition: transform 0.3s ease;
}

.qrcode-image:hover {
  transform: scale(1.05);
}

.qrcode-tip {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0;
  line-height: 1.4;
}

.qrcode-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.save-btn,
.scan-btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.save-btn {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: white;
}

.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 184, 148, 0.3);
}

.scan-btn {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
}

.scan-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(116, 185, 255, 0.3);
}

.steps-section {
  margin-bottom: 2rem;
}

.step-card {
  display: flex;
  align-items: flex-start;
  background: white;
  border-radius: 12px;
  padding: 1.2rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
}

.step-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.step-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 1rem;
  flex-shrink: 0;
}

.step-content h3 {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #333;
}

.step-content p {
  color: #666;
  line-height: 1.5;
}

.notice-section {
  margin-bottom: 2rem;
}

.notice-card {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 1.2rem;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.8rem;
}

.notice-item:last-child {
  margin-bottom: 0;
}

.notice-icon {
  font-size: 1.2rem;
  margin-right: 0.8rem;
  flex-shrink: 0;
}

.reward-section {
  margin-bottom: 2rem;
}

.reward-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.reward-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.reward-header h3 {
  font-size: 1.3rem;
  color: #e74c3c;
  margin-bottom: 0.5rem;
}

.reward-desc {
  color: #666;
}

.reward-requirements,
.reward-process {
  margin-bottom: 1.5rem;
}

.reward-requirements h4,
.reward-process h4 {
  font-size: 1rem;
  margin-bottom: 0.8rem;
  color: #333;
}

.reward-requirements ul,
.reward-process ol {
  padding-left: 1.2rem;
}

.reward-requirements li,
.reward-process li {
  margin-bottom: 0.5rem;
  color: #666;
}

.benefit-highlight {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 1.5rem;
}

.benefit-icon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.quick-actions {
  margin-bottom: 2rem;
}

.action-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-card h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #333;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 0.8rem;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 140px;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  border: none;
  padding: 0.8rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s;
}

.action-btn:hover {
  transform: translateY(-2px);
}

.contact-section {
  margin-bottom: 2rem;
}

.contact-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.contact-card h3 {
  font-size: 1.2rem;
  margin-bottom: 0.8rem;
  color: #333;
}

.contact-card p {
  color: #666;
  margin-bottom: 1.2rem;
}

.contact-btn {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s;
}

.contact-btn:hover {
  transform: translateY(-2px);
}

.footer {
  background: #333;
  color: white;
  text-align: center;
  padding: 1.5rem;
}

.footer p {
  font-size: 0.9rem;
  opacity: 0.8;
}

.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
  z-index: 1000;
}

.back-to-top:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.4);
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.header {
  animation: fadeIn 1s ease;
}

.step-card:nth-child(1) { animation-delay: 0.1s; }
.step-card:nth-child(2) { animation-delay: 0.2s; }
.step-card:nth-child(3) { animation-delay: 0.3s; }
.step-card:nth-child(4) { animation-delay: 0.4s; }

/* 响应式设计 */
@media (max-width: 480px) {
  .header {
    padding: 1.5rem 0.5rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .main-content {
    padding: 0.8rem 0.5rem;
  }

  .header-content {
    padding: 0 0.5rem;
  }

  .qrcode-card {
    padding: 1.5rem 0.8rem;
    margin: 0;
  }

  .qrcode-image {
    width: 100%;
    max-width: 250px;
  }

  .qrcode-actions {
    flex-direction: column;
    align-items: center;
  }

  .save-btn,
  .scan-btn {
    width: 100%;
    max-width: 200px;
  }

  .step-card {
    padding: 1rem 0.8rem;
    margin: 0 0 1rem 0;
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }

  .back-to-top {
    bottom: 1.5rem;
    right: 1.5rem;
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }
}
</style>
