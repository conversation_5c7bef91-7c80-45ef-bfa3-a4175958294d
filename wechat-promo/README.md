# 学习软件激活指南 - 手机版宣传网页

这是一个专为学习软件微信小程序设计的手机版宣传网页，帮助用户了解如何激活和使用学习软件。

## 功能特点

- 📱 **移动端优化**: 专为手机屏幕设计，响应式布局
- 🎨 **现代化设计**: 渐变色彩，圆角卡片，优雅动画
- 📋 **详细步骤**: 清晰的激活步骤说明
- 🎁 **好评奖励**: 突出好评奖励机制
- 🔧 **快捷操作**: 提供实用的快捷功能
- ⬆️ **返回顶部**: 浮动按钮，提升用户体验

## 页面内容

1. **激活步骤指南** - 4步详细说明如何开通学习权限
2. **重要提示** - 双册年卡和兑换码格式说明
3. **好评奖励** - 详细的好评要求和奖励说明
4. **快捷操作** - 兑换码格式复制、扫码技巧
5. **客服联系** - 便捷的客服联系方式

## 技术栈

- Vue 3 - 渐进式JavaScript框架
- Vite - 快速构建工具
- CSS3 - 现代化样式和动画

## 项目设置

```sh
npm install
```

### 开发环境运行

```sh
npm run dev
```

### 生产环境构建

```sh
npm run build
```

## 使用说明

1. 启动开发服务器后，在浏览器中打开 `http://localhost:5173/`
2. 页面会自动适配移动端屏幕
3. 可以在浏览器开发者工具中切换到移动设备模式查看效果

## 自定义修改

### 替换微信小程序码
1. 将您的微信小程序码图片保存为 `wx_qrcode.png`
2. 放置在 `public/images/` 目录下
3. 图片建议尺寸：200x200像素，支持PNG、JPG格式
4. 如果不提供PNG图片，系统会自动显示SVG占位图

### 其他自定义
- 修改 `src/components/WechatPromo.vue` 来调整页面内容和样式
- 修改 `index.html` 来更改页面标题和元信息
- 在 `public/` 目录下替换 favicon.ico 来更改网站图标

## 文件结构
```
wechat-promo/
├── public/
│   ├── images/
│   │   ├── wx_qrcode.png    # 您的微信小程序码（需要您提供）
│   │   └── wx_qrcode.svg    # SVG占位图（自动生成）
│   └── favicon.ico
├── src/
│   ├── components/
│   │   └── WechatPromo.vue  # 主要组件
│   ├── App.vue
│   └── main.js
└── README.md
```
