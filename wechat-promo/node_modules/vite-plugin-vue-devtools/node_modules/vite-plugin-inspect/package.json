{"name": "vite-plugin-inspect", "type": "module", "version": "0.8.9", "description": "Inspect the intermediate state of Vite plugins", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/vite-plugin-inspect#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/vite-plugin-inspect.git"}, "bugs": {"url": "https://github.com/antfu/vite-plugin-inspect/issues"}, "keywords": ["vite-plugin"], "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./nuxt": {"import": {"types": "./dist/nuxt.d.mts", "default": "./dist/nuxt.mjs"}, "require": {"types": "./dist/nuxt.d.cts", "default": "./dist/nuxt.cjs"}}, "./*": "./*"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "files": ["*.d.ts", "dist"], "engines": {"node": ">=14"}, "peerDependencies": {"vite": "^3.1.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.1"}, "peerDependenciesMeta": {"@nuxt/kit": {"optional": true}}, "dependencies": {"@antfu/utils": "^0.7.10", "@rollup/pluginutils": "^5.1.3", "debug": "^4.3.7", "error-stack-parser-es": "^0.1.5", "fs-extra": "^11.2.0", "open": "^10.1.0", "perfect-debounce": "^1.0.0", "picocolors": "^1.1.1", "sirv": "^3.0.0"}, "devDependencies": {"@antfu/eslint-config": "^3.11.2", "@antfu/ni": "^0.23.1", "@iconify-json/catppuccin": "^1.2.7", "@iconify/json": "^2.2.279", "@nuxt/kit": "^3.14.1592", "@types/codemirror": "^5.60.15", "@types/debug": "^4.1.12", "@types/fs-extra": "^11.0.4", "@types/node": "^22.10.1", "@unocss/eslint-config": "^0.65.0", "@unocss/eslint-plugin": "^0.65.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/compiler-sfc": "^3.5.13", "@vueuse/core": "^12.0.0", "@vueuse/router": "^12.0.0", "bumpp": "^9.8.1", "codemirror": "^5.65.16", "codemirror-theme-vars": "^0.1.2", "comlink": "^4.4.2", "cross-env": "^7.0.3", "diff-match-patch-es": "^0.1.1", "echarts": "^5.5.1", "eslint": "^9.16.0", "esno": "^4.8.0", "floating-vue": "^5.2.2", "fuse.js": "^7.0.0", "lint-staged": "^15.2.10", "npm-run-all": "^4.1.5", "ohash": "^1.1.4", "pathe": "^1.1.2", "pinia": "^2.2.8", "prism-theme-vars": "^0.2.5", "rimraf": "^6.0.1", "simple-git-hooks": "^2.11.1", "splitpanes": "^3.1.5", "typescript": "~5.6.3", "unbuild": "^2.0.0", "unocss": "^0.65.0", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vis-data": "7.1.7", "vis-network": "9.1.6", "vite": "^5.4.11", "vite-dev-rpc": "^0.1.7", "vite-hot-client": "^0.2.4", "vite-plugin-pages": "^0.32.4", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0", "vue-tsc": "^2.1.10"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*.{js,ts,vue,md}": ["eslint --cache --fix"]}, "scripts": {"build": "rimraf dist && run-s build:* && esno ./scripts/postbuild.ts", "build:client": "vite build src/client", "build:js": "unbuild", "dev": "nr stub && cross-env INSPECT_DEV=true vite src/client", "dev:client": "vite build src/client --watch", "stub": "unbuild --stub", "lint": "eslint .", "typecheck": "vue-tsc --noEmit", "release": "bumpp && pnpm publish"}}