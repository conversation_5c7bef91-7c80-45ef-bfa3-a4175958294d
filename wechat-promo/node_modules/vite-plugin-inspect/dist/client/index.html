<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="./favicon.svg" type="image/svg+xml">
  <title>Vite Inspect</title>
  <script type="module" crossorigin src="./assets/index-DL5ndA4F.js"></script>
  <link rel="stylesheet" crossorigin href="./assets/index-UVqth9li.css">
</head>
<body data-vite-inspect-mode="DEV">
  <div id="app"></div>
  <script>
    (function () {
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
      const setting = localStorage.getItem('color-schema') || 'auto'
      if (setting === 'dark' || (prefersDark && setting !== 'light'))
        document.documentElement.classList.toggle('dark', true)
    })()

    ;(function () {
      if (!location.pathname.endsWith('/'))
        location.pathname += '/'
    })()
  </script>
</body>
</html>
