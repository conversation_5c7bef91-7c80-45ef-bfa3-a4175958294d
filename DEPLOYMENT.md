# 微信小程序宣传网站部署说明

## 📁 项目位置
项目已成功复制到：`/Users/<USER>/www/vuesite`

## 🚀 快速启动

### 开发环境
```bash
cd /Users/<USER>/www/vuesite
npm run dev
```
访问：http://localhost:5173/

### 生产环境构建
```bash
cd /Users/<USER>/www/vuesite
npm run build
```
构建文件将生成在 `dist/` 目录下

## 📱 微信小程序码

### 当前状态
- ✅ 已检测到真实的微信小程序码：`wx_qrcode.jpg`
- ✅ 图片大小：186KB，格式正确
- ✅ 网站已配置为优先使用JPG格式

### 更换二维码
如需更换微信小程序码：
1. 将新的二维码图片保存为以下任一格式：
   - `wx_qrcode.jpg` (推荐)
   - `wx_qrcode.png`
   - `wx_qrcode.svg`
2. 放置在 `public/images/` 目录下
3. 页面会自动更新

## 🌐 部署到生产环境

### 方式一：静态文件部署
```bash
# 构建项目
npm run build

# 将 dist/ 目录内容上传到您的Web服务器
```

### 方式二：使用Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 方式三：使用Apache
```apache
<VirtualHost *:80>
    DocumentRoot /path/to/dist
    ServerName your-domain.com
    
    <Directory /path/to/dist>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    # 支持Vue Router
    FallbackResource /index.html
</VirtualHost>
```

## 📋 项目特性

### 响应式设计
- ✅ 移动端优化，充分利用屏幕宽度
- ✅ 桌面端自适应布局
- ✅ 支持各种屏幕尺寸

### 功能特点
- 📱 微信小程序码展示
- 📋 详细的激活步骤指南
- 🎁 好评奖励机制说明
- 🔧 快捷操作功能
- ⬆️ 返回顶部按钮
- 📞 客服联系方式

### 技术栈
- Vue 3
- Vite
- 响应式CSS
- 现代JavaScript

## 🔧 自定义修改

### 修改内容
编辑 `src/components/WechatPromo.vue` 文件来修改：
- 页面文字内容
- 步骤说明
- 联系方式
- 样式和颜色

### 修改样式
主要CSS类：
- `.wechat-promo` - 主容器
- `.header` - 头部区域
- `.qrcode-section` - 二维码区域
- `.steps-section` - 步骤说明区域
- `.reward-section` - 好评奖励区域

## 📞 技术支持
如有问题，请检查：
1. Node.js版本是否兼容
2. 依赖是否正确安装
3. 端口5173是否被占用
4. 图片文件是否存在且格式正确

## 🎉 部署完成
您的微信小程序宣传网站已成功部署到 `/Users/<USER>/www/vuesite`，现在可以正常使用了！
